import 'package:flutter_test/flutter_test.dart';
import 'package:tic_tac_toe_game/game_logic.dart';

void main() {
  group('GameLogic Tests', () {
    late GameLogic gameLogic;

    setUp(() {
      gameLogic = GameLogic();
    });

    test('Initial game state should be correct', () {
      expect(gameLogic.currentPlayer, Player.X);
      expect(gameLogic.gameState, GameState.playing);
      expect(gameLogic.board.every((cell) => cell == null), true);
      expect(gameLogic.xWins, 0);
      expect(gameLogic.oWins, 0);
      expect(gameLogic.draws, 0);
    });

    test('Making a valid move should work', () {
      bool moveResult = gameLogic.makeMove(0);
      expect(moveResult, true);
      expect(gameLogic.board[0], Player.X);
      expect(gameLogic.currentPlayer, Player.O);
    });

    test('Making an invalid move should fail', () {
      gameLogic.makeMove(0); // X plays
      bool moveResult = gameLogic.makeMove(0); // Try to play in same spot
      expect(moveResult, false);
      expect(gameLogic.board[0], Player.X); // Should still be X
      expect(gameLogic.currentPlayer, Player.O); // Should still be O's turn
    });

    test('Winning horizontally should be detected', () {
      // X wins top row
      gameLogic.makeMove(0); // X
      gameLogic.makeMove(3); // O
      gameLogic.makeMove(1); // X
      gameLogic.makeMove(4); // O
      gameLogic.makeMove(2); // X wins

      expect(gameLogic.gameState, GameState.xWins);
      expect(gameLogic.xWins, 1);
    });

    test('Winning vertically should be detected', () {
      // X wins left column
      gameLogic.makeMove(0); // X
      gameLogic.makeMove(1); // O
      gameLogic.makeMove(3); // X
      gameLogic.makeMove(2); // O
      gameLogic.makeMove(6); // X wins

      expect(gameLogic.gameState, GameState.xWins);
      expect(gameLogic.xWins, 1);
    });

    test('Winning diagonally should be detected', () {
      // X wins main diagonal
      gameLogic.makeMove(0); // X
      gameLogic.makeMove(1); // O
      gameLogic.makeMove(4); // X
      gameLogic.makeMove(2); // O
      gameLogic.makeMove(8); // X wins

      expect(gameLogic.gameState, GameState.xWins);
      expect(gameLogic.xWins, 1);
    });

    test('Draw should be detected', () {
      // Create a draw scenario
      gameLogic.makeMove(0); // X
      gameLogic.makeMove(1); // O
      gameLogic.makeMove(2); // X
      gameLogic.makeMove(4); // O
      gameLogic.makeMove(3); // X
      gameLogic.makeMove(5); // O
      gameLogic.makeMove(7); // X
      gameLogic.makeMove(6); // O
      gameLogic.makeMove(8); // X

      expect(gameLogic.gameState, GameState.draw);
      expect(gameLogic.draws, 1);
    });

    test('Reset game should work', () {
      gameLogic.makeMove(0);
      gameLogic.makeMove(1);
      gameLogic.resetGame();

      expect(gameLogic.currentPlayer, Player.X);
      expect(gameLogic.gameState, GameState.playing);
      expect(gameLogic.board.every((cell) => cell == null), true);
    });

    test('Reset score should work', () {
      // Play a game where X wins
      gameLogic.makeMove(0); // X
      gameLogic.makeMove(3); // O
      gameLogic.makeMove(1); // X
      gameLogic.makeMove(4); // O
      gameLogic.makeMove(2); // X wins

      expect(gameLogic.xWins, 1);
      
      gameLogic.resetScore();
      
      expect(gameLogic.xWins, 0);
      expect(gameLogic.oWins, 0);
      expect(gameLogic.draws, 0);
      expect(gameLogic.currentPlayer, Player.X);
      expect(gameLogic.gameState, GameState.playing);
    });

    test('Player symbols should be correct', () {
      expect(gameLogic.getPlayerSymbol(Player.X), 'X');
      expect(gameLogic.getPlayerSymbol(Player.O), 'O');
      expect(gameLogic.getPlayerSymbol(null), '');
    });

    test('Current player name should be correct', () {
      expect(gameLogic.getCurrentPlayerName(), 'Player X');
      gameLogic.makeMove(0);
      expect(gameLogic.getCurrentPlayerName(), 'Player O');
    });

    test('Game status messages should be correct', () {
      expect(gameLogic.getGameStatusMessage(), 'Player X\'s Turn');
      
      // X wins
      gameLogic.makeMove(0); // X
      gameLogic.makeMove(3); // O
      gameLogic.makeMove(1); // X
      gameLogic.makeMove(4); // O
      gameLogic.makeMove(2); // X wins
      
      expect(gameLogic.getGameStatusMessage(), 'Player X Wins! 🎉');
    });
  });
}
