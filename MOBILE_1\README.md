# Tic Tac Toe Game 🎮

A beautiful and interactive Tic Tac Toe game built with Flutter that runs on both Android and iOS devices.

## Features ✨

- **Clean, Modern UI**: Dark theme with smooth animations and visual feedback
- **Score Tracking**: Keep track of wins for both players and draws
- **Responsive Design**: Optimized for mobile devices with portrait orientation
- **Smooth Animations**: Engaging visual effects when making moves
- **Game Reset**: Start new games or reset the entire score
- **Win Detection**: Automatic detection of wins (horizontal, vertical, diagonal) and draws

## Screenshots 📱

The game features:
- A 3x3 game board with rounded corners and shadows
- Score display showing wins for Player X, Player O, and draws
- Current player indicator with color coding (Blue for X, Red for O)
- Action buttons for starting new games and resetting scores

## How to Play 🎯

1. **Player X** (Blue) always goes first
2. Tap any empty cell on the 3x3 grid to make your move
3. Players alternate turns (X → O → X → O...)
4. Win by getting three of your symbols in a row:
   - Horizontally (across)
   - Vertically (down)
   - Diagonally
5. The game ends when someone wins or all cells are filled (draw)
6. Use "New Game" to start over or "Reset Score" to clear all statistics

## Getting Started 🚀

### Prerequisites

- Flutter SDK (3.0.0 or higher)
- Android Studio / Xcode for device deployment
- A physical device or emulator

### Installation

1. **Clone or download this project**
2. **Navigate to the project directory**
3. **Install dependencies:**
   ```bash
   flutter pub get
   ```

4. **Run the app:**
   ```bash
   flutter run
   ```

### Building for Release

**Android APK:**
```bash
flutter build apk --release
```

**iOS (requires macOS and Xcode):**
```bash
flutter build ios --release
```

## Project Structure 📁

```
lib/
├── main.dart           # App entry point
├── game_screen.dart    # Main game UI
└── game_logic.dart     # Game logic and state management

test/
└── game_logic_test.dart # Unit tests for game logic

android/                # Android-specific configuration
ios/                    # iOS-specific configuration
```

## Technical Details 🔧

- **Framework**: Flutter
- **Language**: Dart
- **State Management**: StatefulWidget with local state
- **Animations**: AnimationController with Tween animations
- **UI**: Material Design with custom theming
- **Testing**: Unit tests for game logic

## Game Logic 🧠

The game implements:
- **Board State**: 3x3 grid represented as a list of 9 cells
- **Player Management**: Alternating turns between X and O
- **Win Detection**: Checks all possible winning combinations
- **Draw Detection**: Identifies when the board is full with no winner
- **Score Persistence**: Tracks wins and draws across multiple games

## Testing 🧪

Run the unit tests to verify game logic:

```bash
flutter test
```

The tests cover:
- Initial game state
- Valid and invalid moves
- Win detection (horizontal, vertical, diagonal)
- Draw detection
- Game reset functionality
- Score management

## Customization 🎨

You can easily customize:
- **Colors**: Modify the color scheme in `game_screen.dart`
- **Animations**: Adjust animation durations and curves
- **Board Size**: Extend the logic for larger grids (requires code changes)
- **Themes**: Add light/dark theme switching
- **Sound Effects**: Add audio feedback for moves and wins

## Contributing 🤝

Feel free to contribute by:
- Reporting bugs
- Suggesting new features
- Submitting pull requests
- Improving documentation

## License 📄

This project is open source and available under the MIT License.

---

**Enjoy playing Tic Tac Toe! 🎉**
