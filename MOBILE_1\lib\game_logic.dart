enum Player { X, O }

enum GameState { playing, xWins, oWins, draw }

class GameLogic {
  List<Player?> board = List.filled(9, null);
  Player currentPlayer = Player.X;
  GameState gameState = GameState.playing;
  int xWins = 0;
  int oWins = 0;
  int draws = 0;

  // Winning combinations (indices of the 3x3 grid)
  static const List<List<int>> winningCombinations = [
    [0, 1, 2], // Top row
    [3, 4, 5], // Middle row
    [6, 7, 8], // Bottom row
    [0, 3, 6], // Left column
    [1, 4, 7], // Middle column
    [2, 5, 8], // Right column
    [0, 4, 8], // Diagonal top-left to bottom-right
    [2, 4, 6], // Diagonal top-right to bottom-left
  ];

  bool makeMove(int index) {
    if (board[index] != null || gameState != GameState.playing) {
      return false;
    }

    board[index] = currentPlayer;
    
    if (checkWinner()) {
      gameState = currentPlayer == Player.X ? GameState.xWins : GameState.oWins;
      if (currentPlayer == Player.X) {
        xWins++;
      } else {
        oWins++;
      }
    } else if (board.every((cell) => cell != null)) {
      gameState = GameState.draw;
      draws++;
    } else {
      currentPlayer = currentPlayer == Player.X ? Player.O : Player.X;
    }

    return true;
  }

  bool checkWinner() {
    for (List<int> combination in winningCombinations) {
      if (board[combination[0]] != null &&
          board[combination[0]] == board[combination[1]] &&
          board[combination[1]] == board[combination[2]]) {
        return true;
      }
    }
    return false;
  }

  void resetGame() {
    board = List.filled(9, null);
    currentPlayer = Player.X;
    gameState = GameState.playing;
  }

  void resetScore() {
    xWins = 0;
    oWins = 0;
    draws = 0;
    resetGame();
  }

  String getPlayerSymbol(Player? player) {
    if (player == Player.X) return 'X';
    if (player == Player.O) return 'O';
    return '';
  }

  String getCurrentPlayerName() {
    return currentPlayer == Player.X ? 'Player X' : 'Player O';
  }

  String getGameStatusMessage() {
    switch (gameState) {
      case GameState.xWins:
        return 'Player X Wins! 🎉';
      case GameState.oWins:
        return 'Player O Wins! 🎉';
      case GameState.draw:
        return 'It\'s a Draw! 🤝';
      case GameState.playing:
        return '${getCurrentPlayerName()}\'s Turn';
    }
  }
}
